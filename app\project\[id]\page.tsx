"use server";

import React from "react";
import { auth } from "@clerk/nextjs/server";
import { notFound } from "next/navigation";
import { getProject, getProjectOfUser } from "@/actions/projects";
import SectionList from "@/components/pages/project/section-list";
import ProjectHeader from "@/components/pages/project/project-header";
import ProjectFooter from "@/components/pages/project/project-footer";

type Props = {
  params: Promise<{ id: string }>;
};

const ProjectPage = async ({ params }: Props) => {
  const { id } = await params;
  const { userId } = await auth();
  const projects = await getProjectOfUser(userId);

  if (!userId) {
    return notFound();
  }

  const project = await getProject(id, userId);

  if (!project) {
    return notFound();
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <ProjectHeader />

      <main className="pt-8 pb-24 px-4">
        <SectionList sections={project.sections} projectId={project.id} />
      </main>

      <ProjectFooter />
    </div>
  );
};

export default ProjectPage;
