import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Section } from "@prisma/client";
import { Link } from "lucide-react";

type Props = {
  section: Section;
};

const ExternalLink = ({ section }: Props) => {
  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="External Link section"
      >
        <Link className="h-4 w-4" />
      </Button>
    </>
  );
};

export default ExternalLink;
